import { Controller, Get } from '@nestjs/common';

/**
 * This controller provides a health check endpoint that is accessible
 * without the /api prefix for easier access from clients
 */
@Controller()
export class PublicHealthController {
  @Get('health')
  checkHealth() {
    console.log('Health check endpoint called');
    try {
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        service: 'kodi-api',
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
      };
    } catch (error) {
      console.error('Health check error:', error);
      throw error;
    }
  }
}
