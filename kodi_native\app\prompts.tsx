import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from '@/src/contexts/AuthContext';
import { useTheme } from '@/src/contexts/ThemeContext';
import { Colors } from '@/src/constants/Theme';
import Card from '@/src/components/ui/Card';
import api, { endpoints } from '@/src/api/api';

export default function PromptsScreen() {
  const { user, hasAdminRights } = useAuth();
  const { isDarkMode } = useTheme();
  const colors = isDarkMode ? Colors.dark : Colors.light;
  
  const [prompts, setPrompts] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    fetchPrompts();
  }, []);

  const fetchPrompts = async () => {
    if (!user?.companyId) {
      setError('No company associated with your account');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const { data } = await api.get(endpoints.prompts.base);
      setPrompts(data);
    } catch (err: any) {
      console.error('Failed to fetch prompts:', err);
      setError(err.response?.data?.message || 'Failed to load prompts');
      setPrompts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  const handlePromptPress = (promptId: string) => {
    router.push(`/prompt/${promptId}`);
  };

  const handleCreatePrompt = () => {
    if (!hasAdminRights) {
      Alert.alert('Permission Denied', 'You need admin rights to create prompts');
      return;
    }
    router.push('/create-prompt');
  };

  // Placeholder prompts for demonstration


  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity
          style={[styles.backButton, { backgroundColor: colors.secondary }]}
          onPress={handleGoBack}
        >
          <Ionicons name="arrow-back" size={24} color={colors.foreground} />
        </TouchableOpacity>
        <Text style={[styles.title, { color: colors.foreground }]}>Prompts</Text>
        {hasAdminRights && (
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={handleCreatePrompt}
          >
            <Ionicons name="add" size={24} color={colors.primaryForeground} />
          </TouchableOpacity>
        )}
      </View>

      <ScrollView contentContainerStyle={styles.scrollContent}>
        {error ? (
          <Card style={[styles.errorCard, { backgroundColor: colors.destructive + '20' }]}>
            <Text style={[styles.errorText, { color: colors.destructive }]}>{error}</Text>
          </Card>
        ) : null}

        {loading ? (
          <Card style={styles.loadingCard}>
            <Text style={[styles.loadingText, { color: colors.mutedForeground }]}>
              Loading prompts...
            </Text>
          </Card>
        ) : (
          <Card style={styles.contentCard}>
            <Text style={[styles.sectionTitle, { color: colors.foreground }]}>
              AI Prompts & Templates
            </Text>
            <Text style={[styles.description, { color: colors.mutedForeground }]}>
              Manage AI prompts and templates to help guide post creation and analysis.
            </Text>

            {prompts.length === 0 ? (
              <Text style={[styles.emptyText, { color: colors.mutedForeground }]}>
                No prompts found. {hasAdminRights ? 'Create your first prompt to get started.' : 'Contact your administrator to set up prompts.'}
              </Text>
            ) : (
              <View style={styles.promptsContainer}>
                {prompts.map((prompt, index) => (
                  <TouchableOpacity
                    key={index}
                    style={[styles.promptItem, { backgroundColor: colors.secondary }]}
                    onPress={() => handlePromptPress(prompt._id)}
                  >
                    <View style={styles.promptHeader}>
                      <Text style={[styles.promptName, { color: colors.foreground }]}>
                        {prompt.name}
                      </Text>
                      {prompt.isSystem && (
                        <View style={[styles.systemBadge, { backgroundColor: colors.accent + '20' }]}>
                          <Text style={[styles.systemText, { color: colors.accent }]}>
                            System
                          </Text>
                        </View>
                      )}
                      <Ionicons name="chevron-forward" size={20} color={colors.mutedForeground} />
                    </View>

                    {prompt.description && (
                      <Text style={[styles.promptDescription, { color: colors.mutedForeground }]}>
                        {prompt.description}
                      </Text>
                    )}

                    <View style={[styles.templateContainer, { backgroundColor: colors.background }]}>
                      <Text style={[styles.templateLabel, { color: colors.primary }]}>
                        Template:
                      </Text>
                      <Text style={[styles.templateText, { color: colors.mutedForeground }]} numberOfLines={2}>
                        {prompt.template}
                      </Text>
                    </View>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </Card>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    padding: 20,
    gap: 16,
  },
  errorCard: {
    padding: 16,
    borderRadius: 8,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
  loadingCard: {
    padding: 16,
    borderRadius: 8,
  },
  loadingText: {
    fontSize: 16,
    textAlign: 'center',
  },
  contentCard: {
    padding: 16,
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    marginBottom: 16,
    lineHeight: 20,
  },
  promptsContainer: {
    gap: 16,
    marginBottom: 20,
  },
  promptItem: {
    padding: 16,
    borderRadius: 8,
  },
  promptHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  promptName: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: '500',
  },
  promptDescription: {
    fontSize: 14,
    marginBottom: 12,
    lineHeight: 20,
  },
  templateContainer: {
    padding: 12,
    borderRadius: 6,
    marginBottom: 12,
  },
  templateLabel: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  templateText: {
    fontSize: 12,
    lineHeight: 16,
    fontStyle: 'italic',
  },
  promptActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '500',
  },
  emptyText: {
    fontSize: 16,
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 20,
  },
  systemBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginLeft: 8,
  },
  systemText: {
    fontSize: 12,
    fontWeight: '600',
  },
});
