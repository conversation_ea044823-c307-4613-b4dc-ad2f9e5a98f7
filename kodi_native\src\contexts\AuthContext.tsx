import React, { createContext, useContext, useState, useEffect } from 'react';
import { router } from 'expo-router';
import * as SecureStore from 'expo-secure-store';
import api, { endpoints } from '../api/api';
import { User, LoginCredentials, RegisterCredentials } from '../types';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
  updateUserSettings: (settings: any) => Promise<void>;
  isAdmin: boolean;
  isCoAdmin: boolean;
  hasAdminRights: boolean;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  loading: true,
  login: async () => {},
  register: async () => {},
  logout: () => {},
  isAuthenticated: false,
  updateUserSettings: async () => {},
  isAdmin: false,
  isCoAdmin: false,
  hasAdminRights: false,
});

export const useAuth = () => useContext(AuthContext);

const mapBackendUserToUser = (backendUser: any): User => {
  return {
    id: backendUser.userId || backendUser.id,
    email: backendUser.email,
    profile: backendUser.profile,
    companyId: backendUser.companyId,
    roles: backendUser.roles || [],
  };
};

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is logged in on initial load (client-side only)
    const checkAuth = async () => {
      // Skip if we're not in a browser environment
      if (typeof window === 'undefined') {
        setLoading(false);
        return;
      }

      try {
        const accessToken = await SecureStore.getItemAsync('access_token');
        if (accessToken) {
          const { data } = await api.get(endpoints.auth.me);
          setUser(mapBackendUserToUser(data));
        }
      } catch (error) {
        console.error('Auth check failed:', error);

        // Only attempt to clear storage if we're in a browser environment
        if (typeof window !== 'undefined') {
          await SecureStore.deleteItemAsync('access_token');
          await SecureStore.deleteItemAsync('refresh_token');
          await SecureStore.deleteItemAsync('user');
        }
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  const login = async ({ email, password }: LoginCredentials) => {
    try {
      console.log('Attempting login with API URL:', api.defaults.baseURL);

      // Log the request details
      console.log('Login request payload:', { email, password: '******' });

      const { data } = await api.post(endpoints.auth.login, { email, password });

      // Log the response
      console.log('Login successful, received data:', {
        ...data,
        access_token: data.access_token ? '[REDACTED]' : undefined,
        refresh_token: data.refresh_token ? '[REDACTED]' : undefined,
      });

      await SecureStore.setItemAsync('access_token', data.access_token);
      await SecureStore.setItemAsync('refresh_token', data.refresh_token);
      await SecureStore.setItemAsync('user', JSON.stringify(data.user));
      setUser(mapBackendUserToUser(data.user));
      router.replace('/(tabs)');
    } catch (error: any) {
      console.error('Login failed:', error);

      // More detailed error logging
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        console.error('Error response headers:', error.response.headers);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('Error request:', error.request);
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', error.message);
      }

      throw error;
    }
  };

  const register = async ({ email, password, firstName, lastName, inviteCode }: RegisterCredentials) => {
    try {
      const { data } = await api.post(endpoints.auth.register, {
        email,
        password,
        firstName,
        lastName,
        inviteCode
      });
      await SecureStore.setItemAsync('access_token', data.access_token);
      await SecureStore.setItemAsync('refresh_token', data.refresh_token);
      await SecureStore.setItemAsync('user', JSON.stringify(data.user));
      setUser(mapBackendUserToUser(data.user));
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  };

  const logout = async () => {
    await SecureStore.deleteItemAsync('access_token');
    await SecureStore.deleteItemAsync('refresh_token');
    await SecureStore.deleteItemAsync('user');
    setUser(null);
    router.replace('/login');
  };

  const updateUserSettings = async (settings: any) => {
    try {
      if (!user) throw new Error('User not authenticated');
      if (!user.id) throw new Error('User ID not available');

      const { data } = await api.patch(
        endpoints.profiles.updateUserSettings(user.id),
        { userSettings: settings }
      );

      // Update local user state with new settings
      setUser(prev => {
        if (!prev) return null;
        return {
          ...prev,
          profile: {
            ...prev.profile,
            userSettings: {
              ...prev.profile.userSettings,
              ...settings
            }
          }
        };
      });

      return data;
    } catch (error) {
      console.error('Failed to update user settings:', error);
      throw error;
    }
  };

  // Admin role checking helpers
  const isAdmin = user?.roles?.includes('admin') || false;
  const isCoAdmin = user?.roles?.includes('coAdmin') || false;
  const hasAdminRights = isAdmin || isCoAdmin;

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        register,
        logout,
        isAuthenticated: !!user,
        updateUserSettings,
        isAdmin,
        isCoAdmin,
        hasAdminRights,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
