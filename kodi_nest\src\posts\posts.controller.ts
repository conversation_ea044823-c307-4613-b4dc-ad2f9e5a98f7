import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, ForbiddenException, Req } from '@nestjs/common';
import { Request } from 'express';
import { PostsService } from './posts.service';
import { CreatePostDto } from './dto/create-post.dto';
import { UpdatePostDto } from './dto/update-post.dto';
import { CreatePostTypeDto } from './dto/create-post-type.dto';
import { CreateTagDto } from './dto/create-tag.dto';
import { CreateCommentDto } from './dto/create-comment.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';

@Controller('posts')
export class PostsController {
  constructor(private readonly postsService: PostsService) {}

  // Post endpoints
  @UseGuards(JwtAuthGuard)
  @Post()
  createPost(@Body() createPostDto: CreatePostDto) {
    return this.postsService.createPost(createPostDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get()
  findAllPosts(
    @Query('companyId') companyId?: string,
    @Query('teamId') teamId?: string,
    @Query('postTypeId') postTypeId?: string,
    @Query('tag') tag?: string,
    @Query('isDraft') isDraft?: boolean,
  ) {
    return this.postsService.findAllPosts(companyId, teamId, postTypeId, tag, isDraft);
  }

  @UseGuards(JwtAuthGuard)
  @Get('feed')
  getFeed(
    @Req() req: Request,
    @Query('companyId') companyId?: string,
    @Query('teamId') teamId?: string,
    @Query('postTypeId') postTypeId?: string,
    @Query('tag') tag?: string
  ) {
    console.log('Feed endpoint called with params:', { companyId, teamId, postTypeId, tag });

    // If companyId is not provided in the query, use the one from the user context
    const userCompanyId =  req.userContext?.companyId || req.params.companyId;

    // Use the company ID from the query only if it matches the user's company ID
    // Otherwise, always use the user's company ID for security
    const effectiveCompanyId = companyId && userCompanyId && companyId === userCompanyId
      ? companyId
      : userCompanyId;

    if (!effectiveCompanyId) {
      throw new ForbiddenException('Company ID is required');
    }

    // Feed only shows non-draft posts
    return this.postsService.findAllPosts(effectiveCompanyId, teamId, postTypeId, tag, false);
  }

  @UseGuards(JwtAuthGuard)
  @Get('drafts')
  getDrafts(@Req() req: Request, @Query('userId') userId?: string) {
    // Get the user's company ID from the context
        const userCompanyId =  req.userContext?.companyId || req.params.companyId;


    if (!userCompanyId) {
      throw new ForbiddenException('Company ID is required');
    }

    // Get drafts for a specific user within their company
    return this.postsService.findAllPosts(userCompanyId, undefined, undefined, undefined, true);
  }

  // This comment indicates that we've moved the specific routes before the dynamic :id route to prevent conflicts

  // Post Type endpoints
  @UseGuards(JwtAuthGuard)
  @Post('types')
  createPostType(@Body() createPostTypeDto: CreatePostTypeDto) {
    return this.postsService.createPostType(createPostTypeDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('types')
  findAllPostTypes(@Req() req: Request, @Query('companyId') companyId?: string) {
    // If companyId is not provided in the query, use the one from the user context
        const userCompanyId =  req.userContext?.companyId || req.params.companyId;


    // Use the company ID from the query only if it matches the user's company ID
    // Otherwise, always use the user's company ID for security
    const effectiveCompanyId = companyId && userCompanyId && companyId === userCompanyId
      ? companyId
      : userCompanyId;

    if (!effectiveCompanyId) {
      throw new ForbiddenException('Company ID is required');
    }

    return this.postsService.findAllPostTypes(effectiveCompanyId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('types/:id')
  findPostTypeById(@Param('id') id: string) {
    return this.postsService.findPostTypeById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('types/:id')
  updatePostType(
    @Param('id') id: string,
    @Body() updatePostTypeDto: any,
  ) {
    return this.postsService.updatePostType(id, updatePostTypeDto);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'coAdmin')
  @Delete('types/:id')
  removePostType(@Param('id') id: string) {
    return this.postsService.removePostType(id);
  }

  // Tag endpoints
  @UseGuards(JwtAuthGuard)
  @Post('tags')
  createTag(@Body() createTagDto: CreateTagDto) {
    return this.postsService.createTag(createTagDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('tags')
  findAllTags(@Req() req: Request, @Query('companyId') companyId?: string) {
    // If companyId is not provided in the query, use the one from the user context
        const userCompanyId =  req.userContext?.companyId || req.params.companyId;


    // Use the company ID from the query only if it matches the user's company ID
    // Otherwise, always use the user's company ID for security
    const effectiveCompanyId = companyId && userCompanyId && companyId === userCompanyId
      ? companyId
      : userCompanyId;

    if (!effectiveCompanyId) {
      throw new ForbiddenException('Company ID is required');
    }

    return this.postsService.findAllTags(effectiveCompanyId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('tags/:id')
  findTagById(@Param('id') id: string) {
    return this.postsService.findTagById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('tags/:id')
  updateTag(
    @Param('id') id: string,
    @Body() updateTagDto: any,
  ) {
    return this.postsService.updateTag(id, updateTagDto);
  }

  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'coAdmin')
  @Delete('tags/:id')
  removeTag(@Param('id') id: string) {
    return this.postsService.removeTag(id);
  }

  // Comment endpoints
  @UseGuards(JwtAuthGuard)
  @Post('comments')
  createComment(@Body() createCommentDto: CreateCommentDto) {
    return this.postsService.createComment(createCommentDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('comments/:objectType/:objectId')
  findAllComments(
    @Param('objectType') objectType: string,
    @Param('objectId') objectId: string,
  ) {
    return this.postsService.findAllComments(objectType, objectId);
  }

  @UseGuards(JwtAuthGuard)
  @Get('comments/:id')
  findCommentById(@Param('id') id: string) {
    return this.postsService.findCommentById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch('comments/:id')
  updateComment(
    @Param('id') id: string,
    @Body() updateCommentDto: any,
  ) {
    return this.postsService.updateComment(id, updateCommentDto);
  }

  @UseGuards(JwtAuthGuard)
  @Delete('comments/:id')
  removeComment(@Param('id') id: string) {
    return this.postsService.removeComment(id);
  }

  // This dynamic route should be last to avoid conflicts with more specific routes
  @UseGuards(JwtAuthGuard)
  @Get(':id')
  findPostById(@Param('id') id: string) {
    return this.postsService.findPostById(id);
  }

  @UseGuards(JwtAuthGuard)
  @Patch(':id')
  updatePost(
    @Param('id') id: string,
    @Body() updatePostDto: UpdatePostDto,
  ) {
    return this.postsService.updatePost(id, updatePostDto);
  }

  @UseGuards(JwtAuthGuard)
  @Delete(':id')
  removePost(@Param('id') id: string) {
    return this.postsService.removePost(id);
  }
  
}
